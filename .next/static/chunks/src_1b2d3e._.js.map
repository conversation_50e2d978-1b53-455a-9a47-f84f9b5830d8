{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/csv-query-app/src/components/ui/Tabs.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, ReactNode } from 'react';\n\ninterface TabsContextType {\n  value: string;\n  onValueChange: (value: string) => void;\n}\n\nconst TabsContext = createContext<TabsContextType | undefined>(undefined);\n\ninterface TabsProps {\n  value: string;\n  onValueChange: (value: string) => void;\n  children: ReactNode;\n  className?: string;\n}\n\nexport function Tabs({ value, onValueChange, children, className = '' }: TabsProps) {\n  return (\n    <TabsContext.Provider value={{ value, onValueChange }}>\n      <div className={className}>\n        {children}\n      </div>\n    </TabsContext.Provider>\n  );\n}\n\ninterface TabsListProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport function TabsList({ children, className = '' }: TabsListProps) {\n  return (\n    <div className={`flex ${className}`}>\n      {children}\n    </div>\n  );\n}\n\ninterface TabsTriggerProps {\n  value: string;\n  children: ReactNode;\n  className?: string;\n}\n\nexport function TabsTrigger({ value, children, className = '' }: TabsTriggerProps) {\n  const context = useContext(TabsContext);\n  if (!context) {\n    throw new Error('TabsTrigger must be used within Tabs');\n  }\n\n  const { value: currentValue, onValueChange } = context;\n  const isActive = currentValue === value;\n\n  return (\n    <button\n      onClick={() => onValueChange(value)}\n      className={`px-4 py-2 rounded-lg transition-colors ${\n        isActive \n          ? 'bg-blue-500 text-white' \n          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n      } ${className}`}\n    >\n      {children}\n    </button>\n  );\n}\n\ninterface TabsContentProps {\n  value: string;\n  children: ReactNode;\n  className?: string;\n}\n\nexport function TabsContent({ value, children, className = '' }: TabsContentProps) {\n  const context = useContext(TabsContext);\n  if (!context) {\n    throw new Error('TabsContent must be used within Tabs');\n  }\n\n  const { value: currentValue } = context;\n  \n  if (currentValue !== value) {\n    return null;\n  }\n\n  return (\n    <div className={className}>\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;;;AAFA;;AASA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AASxD,SAAS,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAa;IAChF,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAO;QAAc;kBAClD,cAAA,6LAAC;YAAI,WAAW;sBACb;;;;;;;;;;;AAIT;KARgB;AAeT,SAAS,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAiB;IAClE,qBACE,6LAAC;QAAI,WAAW,CAAC,KAAK,EAAE,WAAW;kBAChC;;;;;;AAGP;MANgB;AAcT,SAAS,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAoB;;IAC/E,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,OAAO,YAAY,EAAE,aAAa,EAAE,GAAG;IAC/C,MAAM,WAAW,iBAAiB;IAElC,qBACE,6LAAC;QACC,SAAS,IAAM,cAAc;QAC7B,WAAW,CAAC,uCAAuC,EACjD,WACI,2BACA,8CACL,CAAC,EAAE,WAAW;kBAEd;;;;;;AAGP;GArBgB;MAAA;AA6BT,SAAS,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAoB;;IAC/E,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG;IAEhC,IAAI,iBAAiB,OAAO;QAC1B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW;kBACb;;;;;;AAGP;IAjBgB;MAAA"}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/csv-query-app/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import { Loader2 } from 'lucide-react';\n\ninterface LoadingSpinnerProps {\n  size?: number;\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 24, className = '' }: LoadingSpinnerProps) {\n  return (\n    <div className={`flex items-center justify-center ${className}`}>\n      <Loader2 size={size} className=\"animate-spin text-blue-500\" />\n    </div>\n  );\n}\n\nexport function LoadingCard({ children }: { children: React.ReactNode }) {\n  return (\n    <div className=\"bg-white rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-center py-8\">\n        <LoadingSpinner />\n        <span className=\"ml-2 text-gray-600\">Loading...</span>\n      </div>\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAOe,SAAS,eAAe,EAAE,OAAO,EAAE,EAAE,YAAY,EAAE,EAAuB;IACvF,qBACE,6LAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,6LAAC,oNAAA,CAAA,UAAO;YAAC,MAAM;YAAM,WAAU;;;;;;;;;;;AAGrC;KANwB;AAQjB,SAAS,YAAY,EAAE,QAAQ,EAAiC;IACrE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;;;;kCACD,6LAAC;wBAAK,WAAU;kCAAqB;;;;;;;;;;;;YAEtC;;;;;;;AAGP;MAVgB"}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/csv-query-app/src/components/ChatInterface.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { Send, Bot, User, Loader2 } from 'lucide-react';\nimport { AppState } from '@/types';\nimport LoadingSpinner from '@/components/ui/LoadingSpinner';\n\ninterface ChatMessage {\n  id: string;\n  type: 'user' | 'assistant';\n  content: string;\n  timestamp: Date;\n}\n\ninterface ChatInterfaceProps {\n  appState: AppState;\n  updateAppState: (updates: Partial<AppState>) => void;\n}\n\nexport default function ChatInterface({ appState, updateAppState }: ChatInterfaceProps) {\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [inputValue, setInputValue] = useState('');\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [isClient, setIsClient] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // Initialize client-side only data\n  useEffect(() => {\n    setIsClient(true);\n    setMessages([\n      {\n        id: '1',\n        type: 'assistant',\n        content: 'Hello! I\\'m your AI assistant for contact filtering. You can ask me to find contacts using natural language. For example: \"Find all category managers from grocery chains in Western Canada\" or \"Show me bakeries in Alberta\".',\n        timestamp: new Date(),\n      }\n    ]);\n  }, []);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || isProcessing) return;\n\n    const userMessage: ChatMessage = {\n      id: Date.now().toString(),\n      type: 'user',\n      content: inputValue.trim(),\n      timestamp: new Date(),\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputValue('');\n    setIsProcessing(true);\n\n    try {\n      // Simulate AI processing\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      const assistantMessage: ChatMessage = {\n        id: (Date.now() + 1).toString(),\n        type: 'assistant',\n        content: `I understand you're looking for: \"${userMessage.content}\". Let me process this query and search through your contact database.\n\nBased on your request, I would:\n1. Extract key entities (companies, job titles, locations)\n2. Apply intelligent matching algorithms\n3. Rank results by relevance\n\nTo get started, please upload your CSV contact files in the Data tab, then I can perform actual searches for you!`,\n        timestamp: new Date(),\n      };\n\n      setMessages(prev => [...prev, assistantMessage]);\n    } catch (error) {\n      const errorMessage: ChatMessage = {\n        id: (Date.now() + 1).toString(),\n        type: 'assistant',\n        content: 'Sorry, I encountered an error processing your request. Please try again.',\n        timestamp: new Date(),\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-lg h-[600px] flex flex-col\">\n      {/* Chat Header */}\n      <div className=\"border-b p-4\">\n        <h2 className=\"text-xl font-semibold text-gray-800 flex items-center gap-2\">\n          <Bot className=\"text-blue-500\" size={24} />\n          AI Contact Assistant\n        </h2>\n        <p className=\"text-sm text-gray-600 mt-1\">\n          Ask me to find contacts using natural language\n        </p>\n      </div>\n\n      {/* Messages Area */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n        {!isClient ? (\n          <div className=\"flex items-center justify-center py-8\">\n            <LoadingSpinner />\n            <span className=\"ml-2 text-gray-600\">Loading chat...</span>\n          </div>\n        ) : (\n          messages.map((message) => (\n          <div\n            key={message.id}\n            className={`flex items-start gap-3 ${\n              message.type === 'user' ? 'flex-row-reverse' : 'flex-row'\n            }`}\n          >\n            <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${\n              message.type === 'user'\n                ? 'bg-blue-500 text-white'\n                : 'bg-gray-200 text-gray-600'\n            }`}>\n              {message.type === 'user' ? <User size={16} /> : <Bot size={16} />}\n            </div>\n\n            <div className={`max-w-[80%] ${\n              message.type === 'user' ? 'text-right' : 'text-left'\n            }`}>\n              <div className={`inline-block p-3 rounded-lg ${\n                message.type === 'user'\n                  ? 'bg-blue-500 text-white'\n                  : 'bg-gray-100 text-gray-800'\n              }`}>\n                <p className=\"whitespace-pre-wrap\">{message.content}</p>\n              </div>\n              {isClient && (\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  {message.timestamp.toLocaleTimeString()}\n                </p>\n              )}\n            </div>\n          </div>\n          ))\n        )}\n\n        {isProcessing && (\n          <div className=\"flex items-start gap-3\">\n            <div className=\"flex-shrink-0 w-8 h-8 rounded-full bg-gray-200 text-gray-600 flex items-center justify-center\">\n              <Bot size={16} />\n            </div>\n            <div className=\"bg-gray-100 text-gray-800 p-3 rounded-lg\">\n              <div className=\"flex items-center gap-2\">\n                <Loader2 className=\"animate-spin\" size={16} />\n                <span>Processing your request...</span>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input Area */}\n      <div className=\"border-t p-4\">\n        <div className=\"flex gap-2\">\n          <textarea\n            value={inputValue}\n            onChange={(e) => setInputValue(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Ask me to find contacts... (e.g., 'Find all category managers from grocery chains')\"\n            className=\"flex-1 resize-none border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            rows={2}\n            disabled={isProcessing}\n          />\n          <button\n            onClick={handleSendMessage}\n            disabled={!inputValue.trim() || isProcessing}\n            className=\"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\"\n          >\n            {isProcessing ? (\n              <Loader2 className=\"animate-spin\" size={16} />\n            ) : (\n              <Send size={16} />\n            )}\n          </button>\n        </div>\n\n        {appState.allContacts.length === 0 && (\n          <p className=\"text-sm text-amber-600 mt-2 flex items-center gap-1\">\n            ⚠️ No contact data loaded. Upload CSV files in the Data tab to enable AI filtering.\n          </p>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AAFA;AAAA;AAAA;AAAA;;;AAHA;;;;AAmBe,SAAS,cAAc,EAAE,QAAQ,EAAE,cAAc,EAAsB;;IACpF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,YAAY;YACZ,YAAY;gBACV;oBACE,IAAI;oBACJ,MAAM;oBACN,SAAS;oBACT,WAAW,IAAI;gBACjB;aACD;QACH;kCAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,MAAM,cAAc;QAExC,MAAM,cAA2B;YAC/B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS,WAAW,IAAI;YACxB,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,cAAc;QACd,gBAAgB;QAEhB,IAAI;YACF,yBAAyB;YACzB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,mBAAgC;gBACpC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS,CAAC,kCAAkC,EAAE,YAAY,OAAO,CAAC;;;;;;;iHAOuC,CAAC;gBAC1G,WAAW,IAAI;YACjB;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAiB;QACjD,EAAE,OAAO,OAAO;YACd,MAAM,eAA4B;gBAChC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;gCAAgB,MAAM;;;;;;4BAAM;;;;;;;kCAG7C,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAM5C,6LAAC;gBAAI,WAAU;;oBACZ,CAAC,yBACA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6IAAA,CAAA,UAAc;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAqB;;;;;;;;;;;+BAGvC,SAAS,GAAG,CAAC,CAAC,wBACd,6LAAC;4BAEC,WAAW,CAAC,uBAAuB,EACjC,QAAQ,IAAI,KAAK,SAAS,qBAAqB,YAC/C;;8CAEF,6LAAC;oCAAI,WAAW,CAAC,oEAAoE,EACnF,QAAQ,IAAI,KAAK,SACb,2BACA,6BACJ;8CACC,QAAQ,IAAI,KAAK,uBAAS,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;6DAAS,6LAAC,mMAAA,CAAA,MAAG;wCAAC,MAAM;;;;;;;;;;;8CAG7D,6LAAC;oCAAI,WAAW,CAAC,YAAY,EAC3B,QAAQ,IAAI,KAAK,SAAS,eAAe,aACzC;;sDACA,6LAAC;4CAAI,WAAW,CAAC,4BAA4B,EAC3C,QAAQ,IAAI,KAAK,SACb,2BACA,6BACJ;sDACA,cAAA,6LAAC;gDAAE,WAAU;0DAAuB,QAAQ,OAAO;;;;;;;;;;;wCAEpD,0BACC,6LAAC;4CAAE,WAAU;sDACV,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;2BAzBtC,QAAQ,EAAE;;;;;oBAiClB,8BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,MAAM;;;;;;;;;;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;4CAAe,MAAM;;;;;;sDACxC,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAMd,6LAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,YAAY;gCACZ,aAAY;gCACZ,WAAU;gCACV,MAAM;gCACN,UAAU;;;;;;0CAEZ,6LAAC;gCACC,SAAS;gCACT,UAAU,CAAC,WAAW,IAAI,MAAM;gCAChC,WAAU;0CAET,6BACC,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;oCAAe,MAAM;;;;;yDAExC,6LAAC,qMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;;;;;;;oBAKjB,SAAS,WAAW,CAAC,MAAM,KAAK,mBAC/B,6LAAC;wBAAE,WAAU;kCAAsD;;;;;;;;;;;;;;;;;;AAO7E;GA3LwB;KAAA"}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/csv-query-app/src/components/DataManagement.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useCallback, useEffect } from 'react';\nimport { Upload, FileText, Trash2, Eye, Download } from 'lucide-react';\nimport { AppState, CSVFile, Contact } from '@/types';\nimport Papa from 'papaparse';\n\ninterface DataManagementProps {\n  appState: AppState;\n  updateAppState: (updates: Partial<AppState>) => void;\n}\n\nexport default function DataManagement({ appState, updateAppState }: DataManagementProps) {\n  const [dragActive, setDragActive] = useState(false);\n  const [uploading, setUploading] = useState(false);\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  const handleDrag = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === 'dragenter' || e.type === 'dragover') {\n      setDragActive(true);\n    } else if (e.type === 'dragleave') {\n      setDragActive(false);\n    }\n  }, []);\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      handleFiles(Array.from(e.dataTransfer.files));\n    }\n  }, []);\n\n  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files) {\n      handleFiles(Array.from(e.target.files));\n    }\n  };\n\n  const handleFiles = async (files: File[]) => {\n    setUploading(true);\n\n    for (const file of files) {\n      if (file.type === 'text/csv' || file.name.endsWith('.csv')) {\n        await processCSVFile(file);\n      }\n    }\n\n    setUploading(false);\n  };\n\n  const processCSVFile = (file: File): Promise<void> => {\n    return new Promise((resolve) => {\n      Papa.parse(file, {\n        header: true,\n        skipEmptyLines: true,\n        complete: (results) => {\n          const headers = results.meta.fields || [];\n          const data = results.data as any[];\n\n          // Convert to Contact objects with unique IDs\n          const contacts: Contact[] = data.map((row, index) => ({\n            id: `${file.name}-${index}`,\n            ...row,\n          }));\n\n          const csvFile: CSVFile = {\n            id: `${file.name}-${Date.now()}`,\n            name: file.name,\n            size: file.size,\n            uploadDate: new Date(),\n            headers,\n            rowCount: contacts.length,\n            data: contacts,\n          };\n\n          // Update app state\n          updateAppState({\n            uploadedFiles: [...appState.uploadedFiles, csvFile],\n            allContacts: [...appState.allContacts, ...contacts],\n          });\n\n          resolve();\n        },\n        error: (error) => {\n          console.error('Error parsing CSV:', error);\n          updateAppState({\n            error: `Error parsing ${file.name}: ${error.message}`,\n          });\n          resolve();\n        },\n      });\n    });\n  };\n\n  const removeFile = (fileId: string) => {\n    const fileToRemove = appState.uploadedFiles.find(f => f.id === fileId);\n    if (!fileToRemove) return;\n\n    // Remove contacts from this file\n    const remainingContacts = appState.allContacts.filter(\n      contact => !contact.id.startsWith(fileToRemove.name)\n    );\n\n    updateAppState({\n      uploadedFiles: appState.uploadedFiles.filter(f => f.id !== fileId),\n      allContacts: remainingContacts,\n    });\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Upload Area */}\n      <div className=\"bg-white rounded-lg shadow-lg p-6\">\n        <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">Upload Contact Data</h2>\n\n        <div\n          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${\n            dragActive\n              ? 'border-blue-500 bg-blue-50'\n              : 'border-gray-300 hover:border-gray-400'\n          }`}\n          onDragEnter={handleDrag}\n          onDragLeave={handleDrag}\n          onDragOver={handleDrag}\n          onDrop={handleDrop}\n        >\n          <Upload className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n          <p className=\"text-lg font-medium text-gray-900 mb-2\">\n            Drop CSV files here, or click to select\n          </p>\n          <p className=\"text-sm text-gray-500 mb-4\">\n            Supports CSV files with contact information\n          </p>\n\n          <input\n            type=\"file\"\n            multiple\n            accept=\".csv\"\n            onChange={handleFileInput}\n            className=\"hidden\"\n            id=\"file-upload\"\n          />\n          <label\n            htmlFor=\"file-upload\"\n            className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 cursor-pointer\"\n          >\n            Select Files\n          </label>\n        </div>\n\n        {uploading && (\n          <div className=\"mt-4 text-center\">\n            <div className=\"inline-flex items-center px-4 py-2 text-sm text-blue-600\">\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2\"></div>\n              Processing files...\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Uploaded Files */}\n      <div className=\"bg-white rounded-lg shadow-lg p-6\">\n        <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">\n          Uploaded Files ({appState.uploadedFiles.length})\n        </h2>\n\n        {appState.uploadedFiles.length === 0 ? (\n          <div className=\"text-center py-8 text-gray-500\">\n            <FileText className=\"mx-auto h-12 w-12 text-gray-300 mb-4\" />\n            <p>No files uploaded yet</p>\n            <p className=\"text-sm\">Upload CSV files to get started</p>\n          </div>\n        ) : (\n          <div className=\"space-y-3\">\n            {appState.uploadedFiles.map((file) => (\n              <div\n                key={file.id}\n                className=\"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50\"\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <FileText className=\"h-8 w-8 text-blue-500\" />\n                  <div>\n                    <h3 className=\"font-medium text-gray-900\">{file.name}</h3>\n                    <p className=\"text-sm text-gray-500\">\n                      {file.rowCount} contacts • {formatFileSize(file.size)}\n                      {isClient && (\n                        <> • Uploaded {file.uploadDate.toLocaleDateString()}</>\n                      )}\n                    </p>\n                    <p className=\"text-xs text-gray-400\">\n                      Fields: {file.headers.join(', ')}\n                    </p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    className=\"p-2 text-gray-400 hover:text-blue-500 rounded-lg hover:bg-blue-50\"\n                    title=\"Preview data\"\n                  >\n                    <Eye size={16} />\n                  </button>\n                  <button\n                    className=\"p-2 text-gray-400 hover:text-green-500 rounded-lg hover:bg-green-50\"\n                    title=\"Download\"\n                  >\n                    <Download size={16} />\n                  </button>\n                  <button\n                    onClick={() => removeFile(file.id)}\n                    className=\"p-2 text-gray-400 hover:text-red-500 rounded-lg hover:bg-red-50\"\n                    title=\"Remove file\"\n                  >\n                    <Trash2 size={16} />\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Data Summary */}\n      {appState.allContacts.length > 0 && (\n        <div className=\"bg-white rounded-lg shadow-lg p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">Data Summary</h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"bg-blue-50 p-4 rounded-lg\">\n              <h3 className=\"font-medium text-blue-900\">Total Contacts</h3>\n              <p className=\"text-2xl font-bold text-blue-600\">\n                {appState.allContacts.length.toLocaleString()}\n              </p>\n            </div>\n\n            <div className=\"bg-green-50 p-4 rounded-lg\">\n              <h3 className=\"font-medium text-green-900\">Files Loaded</h3>\n              <p className=\"text-2xl font-bold text-green-600\">\n                {appState.uploadedFiles.length}\n              </p>\n            </div>\n\n            <div className=\"bg-purple-50 p-4 rounded-lg\">\n              <h3 className=\"font-medium text-purple-900\">Ready for AI</h3>\n              <p className=\"text-2xl font-bold text-purple-600\">\n                ✓ Yes\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AAFA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;;AAYe,SAAS,eAAe,EAAE,QAAQ,EAAE,cAAc,EAAuB;;IACtF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,YAAY;QACd;mCAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAC9B,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,YAAY;gBACnD,cAAc;YAChB,OAAO,IAAI,EAAE,IAAI,KAAK,aAAa;gBACjC,cAAc;YAChB;QACF;iDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAC9B,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,cAAc;YAEd,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;gBACnD,YAAY,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;YAC7C;QACF;iDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;YAClB,YAAY,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK;QACvC;IACF;IAEA,MAAM,cAAc,OAAO;QACzB,aAAa;QAEb,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,IAAI,KAAK,cAAc,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;gBAC1D,MAAM,eAAe;YACvB;QACF;QAEA,aAAa;IACf;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,QAAQ,CAAC;YAClB,gJAAA,CAAA,UAAI,CAAC,KAAK,CAAC,MAAM;gBACf,QAAQ;gBACR,gBAAgB;gBAChB,UAAU,CAAC;oBACT,MAAM,UAAU,QAAQ,IAAI,CAAC,MAAM,IAAI,EAAE;oBACzC,MAAM,OAAO,QAAQ,IAAI;oBAEzB,6CAA6C;oBAC7C,MAAM,WAAsB,KAAK,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;4BACpD,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO;4BAC3B,GAAG,GAAG;wBACR,CAAC;oBAED,MAAM,UAAmB;wBACvB,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;wBAChC,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;wBACf,YAAY,IAAI;wBAChB;wBACA,UAAU,SAAS,MAAM;wBACzB,MAAM;oBACR;oBAEA,mBAAmB;oBACnB,eAAe;wBACb,eAAe;+BAAI,SAAS,aAAa;4BAAE;yBAAQ;wBACnD,aAAa;+BAAI,SAAS,WAAW;+BAAK;yBAAS;oBACrD;oBAEA;gBACF;gBACA,OAAO,CAAC;oBACN,QAAQ,KAAK,CAAC,sBAAsB;oBACpC,eAAe;wBACb,OAAO,CAAC,cAAc,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,EAAE;oBACvD;oBACA;gBACF;YACF;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,eAAe,SAAS,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/D,IAAI,CAAC,cAAc;QAEnB,iCAAiC;QACjC,MAAM,oBAAoB,SAAS,WAAW,CAAC,MAAM,CACnD,CAAA,UAAW,CAAC,QAAQ,EAAE,CAAC,UAAU,CAAC,aAAa,IAAI;QAGrD,eAAe;YACb,eAAe,SAAS,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC3D,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAEzD,6LAAC;wBACC,WAAW,CAAC,oEAAoE,EAC9E,aACI,+BACA,yCACJ;wBACF,aAAa;wBACb,aAAa;wBACb,YAAY;wBACZ,QAAQ;;0CAER,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAE,WAAU;0CAAyC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAI1C,6LAAC;gCACC,MAAK;gCACL,QAAQ;gCACR,QAAO;gCACP,UAAU;gCACV,WAAU;gCACV,IAAG;;;;;;0CAEL,6LAAC;gCACC,SAAQ;gCACR,WAAU;0CACX;;;;;;;;;;;;oBAKF,2BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;gCAA0E;;;;;;;;;;;;;;;;;;0BAQjG,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAA2C;4BACtC,SAAS,aAAa,CAAC,MAAM;4BAAC;;;;;;;oBAGhD,SAAS,aAAa,CAAC,MAAM,KAAK,kBACjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;0CAAE;;;;;;0CACH,6LAAC;gCAAE,WAAU;0CAAU;;;;;;;;;;;6CAGzB,6LAAC;wBAAI,WAAU;kCACZ,SAAS,aAAa,CAAC,GAAG,CAAC,CAAC,qBAC3B,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6B,KAAK,IAAI;;;;;;kEACpD,6LAAC;wDAAE,WAAU;;4DACV,KAAK,QAAQ;4DAAC;4DAAa,eAAe,KAAK,IAAI;4DACnD,0BACC;;oEAAE;oEAAa,KAAK,UAAU,CAAC,kBAAkB;;;;;;;;;kEAGrD,6LAAC;wDAAE,WAAU;;4DAAwB;4DAC1B,KAAK,OAAO,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;kDAKjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAU;gDACV,OAAM;0DAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,MAAM;;;;;;;;;;;0DAEb,6LAAC;gDACC,WAAU;gDACV,OAAM;0DAEN,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,MAAM;;;;;;;;;;;0DAElB,6LAAC;gDACC,SAAS,IAAM,WAAW,KAAK,EAAE;gDACjC,WAAU;gDACV,OAAM;0DAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,MAAM;;;;;;;;;;;;;;;;;;+BArCb,KAAK,EAAE;;;;;;;;;;;;;;;;YA+CrB,SAAS,WAAW,CAAC,MAAM,GAAG,mBAC7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAEzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6LAAC;wCAAE,WAAU;kDACV,SAAS,WAAW,CAAC,MAAM,CAAC,cAAc;;;;;;;;;;;;0CAI/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;kDACV,SAAS,aAAa,CAAC,MAAM;;;;;;;;;;;;0CAIlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShE;GAlQwB;KAAA"}}, {"offset": {"line": 1084, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/csv-query-app/src/components/ResultsView.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Search, Download, Filter, Star, Clock, CheckSquare, Square } from 'lucide-react';\nimport { AppState, MatchResult, Contact } from '@/types';\n\ninterface ResultsViewProps {\n  appState: AppState;\n  updateAppState: (updates: Partial<AppState>) => void;\n}\n\nexport default function ResultsView({ appState, updateAppState }: ResultsViewProps) {\n  const [selectedContacts, setSelectedContacts] = useState<Set<string>>(new Set());\n  const [sortBy, setSortBy] = useState<'score' | 'name' | 'company'>('score');\n  const [filterMinScore, setFilterMinScore] = useState(0);\n\n  // Mock results for demonstration\n  const mockResults: MatchResult[] = [\n    {\n      contact: {\n        id: '1',\n        name: '<PERSON>',\n        company: 'Metro Grocery Chain',\n        title: 'Category Manager',\n        email: '<EMAIL>',\n        phone: '(*************',\n        city: 'Toronto',\n        state: 'ON',\n        industry: 'Grocery Retail',\n      },\n      score: 0.95,\n      matchedFields: ['title', 'company', 'industry'],\n      explanation: 'Strong match: Category Manager at grocery chain with relevant industry classification',\n    },\n    {\n      contact: {\n        id: '2',\n        name: 'Sarah <PERSON>',\n        company: 'Loblaws Companies',\n        title: 'Senior Category Manager',\n        email: '<EMAIL>',\n        phone: '(*************',\n        city: 'Mississauga',\n        state: 'ON',\n        industry: 'Retail',\n      },\n      score: 0.88,\n      matchedFields: ['title', 'company'],\n      explanation: 'Good match: Senior Category Manager at major grocery retailer',\n    },\n    {\n      contact: {\n        id: '3',\n        name: 'Mike Chen',\n        company: 'Sobeys Inc.',\n        title: 'Category Specialist',\n        email: '<EMAIL>',\n        phone: '(*************',\n        city: 'Halifax',\n        state: 'NS',\n        industry: 'Food Retail',\n      },\n      score: 0.82,\n      matchedFields: ['title', 'industry'],\n      explanation: 'Moderate match: Category role in food retail industry',\n    },\n  ];\n\n  const results = appState.currentResults?.contacts || mockResults;\n  const filteredResults = results.filter(result => result.score >= filterMinScore);\n\n  const toggleContactSelection = (contactId: string) => {\n    const newSelected = new Set(selectedContacts);\n    if (newSelected.has(contactId)) {\n      newSelected.delete(contactId);\n    } else {\n      newSelected.add(contactId);\n    }\n    setSelectedContacts(newSelected);\n  };\n\n  const selectAll = () => {\n    if (selectedContacts.size === filteredResults.length) {\n      setSelectedContacts(new Set());\n    } else {\n      setSelectedContacts(new Set(filteredResults.map(r => r.contact.id)));\n    }\n  };\n\n  const exportSelected = () => {\n    const selectedResults = filteredResults.filter(r => \n      selectedContacts.has(r.contact.id)\n    );\n    \n    // Create CSV content\n    const headers = ['Name', 'Company', 'Title', 'Email', 'Phone', 'City', 'State', 'Match Score'];\n    const csvContent = [\n      headers.join(','),\n      ...selectedResults.map(result => [\n        result.contact.name || '',\n        result.contact.company || '',\n        result.contact.title || '',\n        result.contact.email || '',\n        result.contact.phone || '',\n        result.contact.city || '',\n        result.contact.state || '',\n        result.score.toFixed(2),\n      ].map(field => `\"${field}\"`).join(','))\n    ].join('\\n');\n\n    // Download file\n    const blob = new Blob([csvContent], { type: 'text/csv' });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `filtered-contacts-${new Date().toISOString().split('T')[0]}.csv`;\n    a.click();\n    window.URL.revokeObjectURL(url);\n  };\n\n  const getScoreColor = (score: number) => {\n    if (score >= 0.9) return 'text-green-600 bg-green-100';\n    if (score >= 0.7) return 'text-yellow-600 bg-yellow-100';\n    return 'text-red-600 bg-red-100';\n  };\n\n  const getScoreStars = (score: number) => {\n    const stars = Math.round(score * 5);\n    return Array.from({ length: 5 }, (_, i) => (\n      <Star\n        key={i}\n        size={12}\n        className={i < stars ? 'text-yellow-400 fill-current' : 'text-gray-300'}\n      />\n    ));\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Results Header */}\n      <div className=\"bg-white rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h2 className=\"text-xl font-semibold text-gray-800\">Search Results</h2>\n            <p className=\"text-sm text-gray-600\">\n              {filteredResults.length} contacts found\n              {appState.currentResults && (\n                <span> • Query: \"{appState.currentResults.query}\"</span>\n              )}\n            </p>\n          </div>\n          \n          <div className=\"flex items-center gap-3\">\n            <button\n              onClick={exportSelected}\n              disabled={selectedContacts.size === 0}\n              className=\"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <Download size={16} />\n              Export ({selectedContacts.size})\n            </button>\n          </div>\n        </div>\n\n        {/* Filters and Controls */}\n        <div className=\"flex items-center gap-4 flex-wrap\">\n          <div className=\"flex items-center gap-2\">\n            <Filter size={16} className=\"text-gray-500\" />\n            <label className=\"text-sm text-gray-600\">Min Score:</label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.1\"\n              value={filterMinScore}\n              onChange={(e) => setFilterMinScore(parseFloat(e.target.value))}\n              className=\"w-20\"\n            />\n            <span className=\"text-sm text-gray-600\">{filterMinScore.toFixed(1)}</span>\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            <label className=\"text-sm text-gray-600\">Sort by:</label>\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value as any)}\n              className=\"text-sm border rounded px-2 py-1\"\n            >\n              <option value=\"score\">Match Score</option>\n              <option value=\"name\">Name</option>\n              <option value=\"company\">Company</option>\n            </select>\n          </div>\n\n          <button\n            onClick={selectAll}\n            className=\"flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800\"\n          >\n            {selectedContacts.size === filteredResults.length ? (\n              <CheckSquare size={16} />\n            ) : (\n              <Square size={16} />\n            )}\n            {selectedContacts.size === filteredResults.length ? 'Deselect All' : 'Select All'}\n          </button>\n        </div>\n      </div>\n\n      {/* Results List */}\n      <div className=\"bg-white rounded-lg shadow-lg\">\n        {filteredResults.length === 0 ? (\n          <div className=\"p-8 text-center text-gray-500\">\n            <Search className=\"mx-auto h-12 w-12 text-gray-300 mb-4\" />\n            <p className=\"text-lg\">No results found</p>\n            <p className=\"text-sm\">Try adjusting your filters or search query</p>\n          </div>\n        ) : (\n          <div className=\"divide-y\">\n            {filteredResults.map((result) => (\n              <div\n                key={result.contact.id}\n                className={`p-6 hover:bg-gray-50 transition-colors ${\n                  selectedContacts.has(result.contact.id) ? 'bg-blue-50' : ''\n                }`}\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex items-start gap-4 flex-1\">\n                    <button\n                      onClick={() => toggleContactSelection(result.contact.id)}\n                      className=\"mt-1\"\n                    >\n                      {selectedContacts.has(result.contact.id) ? (\n                        <CheckSquare className=\"text-blue-600\" size={20} />\n                      ) : (\n                        <Square className=\"text-gray-400\" size={20} />\n                      )}\n                    </button>\n\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center gap-3 mb-2\">\n                        <h3 className=\"font-semibold text-gray-900\">\n                          {result.contact.name}\n                        </h3>\n                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(result.score)}`}>\n                          {(result.score * 100).toFixed(0)}% match\n                        </div>\n                        <div className=\"flex items-center gap-1\">\n                          {getScoreStars(result.score)}\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600 mb-3\">\n                        <div>\n                          <strong>Company:</strong> {result.contact.company}\n                        </div>\n                        <div>\n                          <strong>Title:</strong> {result.contact.title}\n                        </div>\n                        <div>\n                          <strong>Email:</strong> {result.contact.email}\n                        </div>\n                        <div>\n                          <strong>Location:</strong> {result.contact.city}, {result.contact.state}\n                        </div>\n                      </div>\n\n                      <div className=\"text-sm\">\n                        <p className=\"text-gray-700 mb-2\">{result.explanation}</p>\n                        <div className=\"flex items-center gap-2\">\n                          <span className=\"text-gray-500\">Matched fields:</span>\n                          {result.matchedFields.map((field) => (\n                            <span\n                              key={field}\n                              className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs\"\n                            >\n                              {field}\n                            </span>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Query Performance */}\n      {appState.currentResults && (\n        <div className=\"bg-white rounded-lg shadow-lg p-6\">\n          <h3 className=\"font-semibold text-gray-800 mb-3\">Query Performance</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n            <div className=\"flex items-center gap-2\">\n              <Clock size={16} className=\"text-gray-500\" />\n              <span>Execution Time: {appState.currentResults.executionTime}ms</span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <Star size={16} className=\"text-gray-500\" />\n              <span>Avg Confidence: {(appState.currentResults.confidence * 100).toFixed(1)}%</span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <Search size={16} className=\"text-gray-500\" />\n              <span>Total Matches: {appState.currentResults.totalMatches}</span>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAWe,SAAS,YAAY,EAAE,QAAQ,EAAE,cAAc,EAAoB;;IAChF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC1E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,iCAAiC;IACjC,MAAM,cAA6B;QACjC;YACE,SAAS;gBACP,IAAI;gBACJ,MAAM;gBACN,SAAS;gBACT,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;gBACN,OAAO;gBACP,UAAU;YACZ;YACA,OAAO;YACP,eAAe;gBAAC;gBAAS;gBAAW;aAAW;YAC/C,aAAa;QACf;QACA;YACE,SAAS;gBACP,IAAI;gBACJ,MAAM;gBACN,SAAS;gBACT,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;gBACN,OAAO;gBACP,UAAU;YACZ;YACA,OAAO;YACP,eAAe;gBAAC;gBAAS;aAAU;YACnC,aAAa;QACf;QACA;YACE,SAAS;gBACP,IAAI;gBACJ,MAAM;gBACN,SAAS;gBACT,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;gBACN,OAAO;gBACP,UAAU;YACZ;YACA,OAAO;YACP,eAAe;gBAAC;gBAAS;aAAW;YACpC,aAAa;QACf;KACD;IAED,MAAM,UAAU,SAAS,cAAc,EAAE,YAAY;IACrD,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,KAAK,IAAI;IAEjE,MAAM,yBAAyB,CAAC;QAC9B,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,YAAY;YAC9B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,oBAAoB;IACtB;IAEA,MAAM,YAAY;QAChB,IAAI,iBAAiB,IAAI,KAAK,gBAAgB,MAAM,EAAE;YACpD,oBAAoB,IAAI;QAC1B,OAAO;YACL,oBAAoB,IAAI,IAAI,gBAAgB,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,CAAC,EAAE;QACnE;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,kBAAkB,gBAAgB,MAAM,CAAC,CAAA,IAC7C,iBAAiB,GAAG,CAAC,EAAE,OAAO,CAAC,EAAE;QAGnC,qBAAqB;QACrB,MAAM,UAAU;YAAC;YAAQ;YAAW;YAAS;YAAS;YAAS;YAAQ;YAAS;SAAc;QAC9F,MAAM,aAAa;YACjB,QAAQ,IAAI,CAAC;eACV,gBAAgB,GAAG,CAAC,CAAA,SAAU;oBAC/B,OAAO,OAAO,CAAC,IAAI,IAAI;oBACvB,OAAO,OAAO,CAAC,OAAO,IAAI;oBAC1B,OAAO,OAAO,CAAC,KAAK,IAAI;oBACxB,OAAO,OAAO,CAAC,KAAK,IAAI;oBACxB,OAAO,OAAO,CAAC,KAAK,IAAI;oBACxB,OAAO,OAAO,CAAC,IAAI,IAAI;oBACvB,OAAO,OAAO,CAAC,KAAK,IAAI;oBACxB,OAAO,KAAK,CAAC,OAAO,CAAC;iBACtB,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;SACnC,CAAC,IAAI,CAAC;QAEP,gBAAgB;QAChB,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAW;QACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QACvC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,kBAAkB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QAC9E,EAAE,KAAK;QACP,OAAO,GAAG,CAAC,eAAe,CAAC;IAC7B;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,KAAK,OAAO;QACzB,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,QAAQ,KAAK,KAAK,CAAC,QAAQ;QACjC,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,kBACnC,6LAAC,qMAAA,CAAA,OAAI;gBAEH,MAAM;gBACN,WAAW,IAAI,QAAQ,iCAAiC;eAFnD;;;;;IAKX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,6LAAC;wCAAE,WAAU;;4CACV,gBAAgB,MAAM;4CAAC;4CACvB,SAAS,cAAc,kBACtB,6LAAC;;oDAAK;oDAAY,SAAS,cAAc,CAAC,KAAK;oDAAC;;;;;;;;;;;;;;;;;;;0CAKtD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,UAAU,iBAAiB,IAAI,KAAK;oCACpC,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;wCAAM;wCACb,iBAAiB,IAAI;wCAAC;;;;;;;;;;;;;;;;;;kCAMrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC5B,6LAAC;wCAAM,WAAU;kDAAwB;;;;;;kDACzC,6LAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC5D,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAyB,eAAe,OAAO,CAAC;;;;;;;;;;;;0CAGlE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAwB;;;;;;kDACzC,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAU;;;;;;;;;;;;;;;;;;0CAI5B,6LAAC;gCACC,SAAS;gCACT,WAAU;;oCAET,iBAAiB,IAAI,KAAK,gBAAgB,MAAM,iBAC/C,6LAAC,8NAAA,CAAA,cAAW;wCAAC,MAAM;;;;;6DAEnB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;oCAEf,iBAAiB,IAAI,KAAK,gBAAgB,MAAM,GAAG,iBAAiB;;;;;;;;;;;;;;;;;;;0BAM3E,6LAAC;gBAAI,WAAU;0BACZ,gBAAgB,MAAM,KAAK,kBAC1B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BAAE,WAAU;sCAAU;;;;;;sCACvB,6LAAC;4BAAE,WAAU;sCAAU;;;;;;;;;;;yCAGzB,6LAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;4BAEC,WAAW,CAAC,uCAAuC,EACjD,iBAAiB,GAAG,CAAC,OAAO,OAAO,CAAC,EAAE,IAAI,eAAe,IACzD;sCAEF,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,uBAAuB,OAAO,OAAO,CAAC,EAAE;4CACvD,WAAU;sDAET,iBAAiB,GAAG,CAAC,OAAO,OAAO,CAAC,EAAE,kBACrC,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;gDAAgB,MAAM;;;;;qEAE7C,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;gDAAgB,MAAM;;;;;;;;;;;sDAI5C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,OAAO,OAAO,CAAC,IAAI;;;;;;sEAEtB,6LAAC;4DAAI,WAAW,CAAC,2CAA2C,EAAE,cAAc,OAAO,KAAK,GAAG;;gEACxF,CAAC,OAAO,KAAK,GAAG,GAAG,EAAE,OAAO,CAAC;gEAAG;;;;;;;sEAEnC,6LAAC;4DAAI,WAAU;sEACZ,cAAc,OAAO,KAAK;;;;;;;;;;;;8DAI/B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;8EAAO;;;;;;gEAAiB;gEAAE,OAAO,OAAO,CAAC,OAAO;;;;;;;sEAEnD,6LAAC;;8EACC,6LAAC;8EAAO;;;;;;gEAAe;gEAAE,OAAO,OAAO,CAAC,KAAK;;;;;;;sEAE/C,6LAAC;;8EACC,6LAAC;8EAAO;;;;;;gEAAe;gEAAE,OAAO,OAAO,CAAC,KAAK;;;;;;;sEAE/C,6LAAC;;8EACC,6LAAC;8EAAO;;;;;;gEAAkB;gEAAE,OAAO,OAAO,CAAC,IAAI;gEAAC;gEAAG,OAAO,OAAO,CAAC,KAAK;;;;;;;;;;;;;8DAI3E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAsB,OAAO,WAAW;;;;;;sEACrD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;gEAC/B,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC,sBACzB,6LAAC;wEAEC,WAAU;kFAET;uEAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BApDd,OAAO,OAAO,CAAC,EAAE;;;;;;;;;;;;;;;YAsE/B,SAAS,cAAc,kBACtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC3B,6LAAC;;4CAAK;4CAAiB,SAAS,cAAc,CAAC,aAAa;4CAAC;;;;;;;;;;;;;0CAE/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC1B,6LAAC;;4CAAK;4CAAiB,CAAC,SAAS,cAAc,CAAC,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAE/E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC5B,6LAAC;;4CAAK;4CAAgB,SAAS,cAAc,CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxE;GA5SwB;KAAA"}}, {"offset": {"line": 1846, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1852, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/csv-query-app/src/components/SettingsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Settings, Save, RefreshC<PERSON>, Key, Slide<PERSON>, Palette } from 'lucide-react';\nimport { AppState, AIConfig } from '@/types';\nimport { testAIConnection } from '@/lib/aiProviders';\n\ninterface SettingsPanelProps {\n  appState: AppState;\n  updateAppState: (updates: Partial<AppState>) => void;\n}\n\nexport default function SettingsPanel({ appState, updateAppState }: SettingsPanelProps) {\n  const [config, setConfig] = useState<AIConfig>({\n    provider: 'openai',\n    apiKey: '',\n    model: 'gpt-4',\n    temperature: 0.3,\n    maxTokens: 2000,\n    baseURL: '',\n  });\n\n  const [matchingSettings, setMatchingSettings] = useState({\n    fuzzyThreshold: 0.7,\n    semanticThreshold: 0.8,\n    maxResults: 50,\n  });\n\n  const [uiSettings, setUISettings] = useState({\n    theme: 'light' as 'light' | 'dark' | 'auto',\n    language: 'en',\n  });\n\n  const [showApiKey, setShowApiKey] = useState(false);\n\n  const handleSaveSettings = () => {\n    // In a real app, this would save to localStorage or backend\n    console.log('Saving settings:', { config, matchingSettings, uiSettings });\n\n    // Show success message\n    updateAppState({\n      error: null, // Clear any existing errors\n    });\n\n    // You could show a toast notification here\n    alert('Settings saved successfully!');\n  };\n\n  const handleTestConnection = async () => {\n    if (!config.apiKey) {\n      alert('Please enter an API key first');\n      return;\n    }\n\n    // Simulate API test\n    try {\n      updateAppState({ isProcessing: true });\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      alert('Connection test successful!');\n    } catch (error) {\n      alert('Connection test failed. Please check your API key and settings.');\n    } finally {\n      updateAppState({ isProcessing: false });\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* AI Configuration */}\n      <div className=\"bg-white rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center gap-2 mb-4\">\n          <Key className=\"text-blue-500\" size={20} />\n          <h2 className=\"text-xl font-semibold text-gray-800\">AI Configuration</h2>\n        </div>\n\n        <div className=\"space-y-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              AI Provider\n            </label>\n            <select\n              value={config.provider}\n              onChange={(e) => setConfig(prev => ({\n                ...prev,\n                provider: e.target.value as any,\n                // Reset model when provider changes\n                model: e.target.value === 'openai' ? 'gpt-4' :\n                       e.target.value === 'anthropic' ? 'claude-3-sonnet' :\n                       e.target.value === 'openrouter' ? 'openai/gpt-4' :\n                       e.target.value === 'gemini' ? 'gemini-pro' : 'llama-2',\n                // Set default baseURL for OpenRouter\n                baseURL: e.target.value === 'openrouter' ? 'https://openrouter.ai/api/v1' : ''\n              }))}\n              className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"openai\">OpenAI</option>\n              <option value=\"anthropic\">Anthropic</option>\n              <option value=\"openrouter\">OpenRouter</option>\n              <option value=\"gemini\">Google Gemini</option>\n              <option value=\"local\">Local Model</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Model\n            </label>\n            {config.provider === 'openrouter' ? (\n              <input\n                type=\"text\"\n                value={config.model}\n                onChange={(e) => setConfig(prev => ({ ...prev, model: e.target.value }))}\n                placeholder=\"e.g., openai/gpt-4, anthropic/claude-3-sonnet\"\n                className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            ) : (\n              <select\n                value={config.model}\n                onChange={(e) => setConfig(prev => ({ ...prev, model: e.target.value }))}\n                className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                {config.provider === 'openai' && (\n                  <>\n                    <option value=\"gpt-4\">GPT-4</option>\n                    <option value=\"gpt-4-turbo\">GPT-4 Turbo</option>\n                    <option value=\"gpt-3.5-turbo\">GPT-3.5 Turbo</option>\n                  </>\n                )}\n                {config.provider === 'anthropic' && (\n                  <>\n                    <option value=\"claude-3-opus\">Claude 3 Opus</option>\n                    <option value=\"claude-3-sonnet\">Claude 3 Sonnet</option>\n                    <option value=\"claude-3-haiku\">Claude 3 Haiku</option>\n                  </>\n                )}\n                {config.provider === 'gemini' && (\n                  <>\n                    <option value=\"gemini-pro\">Gemini Pro</option>\n                    <option value=\"gemini-pro-vision\">Gemini Pro Vision</option>\n                    <option value=\"gemini-1.5-pro\">Gemini 1.5 Pro</option>\n                    <option value=\"gemini-1.5-flash\">Gemini 1.5 Flash</option>\n                  </>\n                )}\n                {config.provider === 'local' && (\n                  <option value=\"llama-2\">Llama 2</option>\n                )}\n              </select>\n            )}\n            {config.provider === 'openrouter' && (\n              <p className=\"text-xs text-gray-500 mt-1\">\n                Enter the model ID from <a href=\"https://openrouter.ai/models\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-500 hover:underline\">OpenRouter models list</a>\n              </p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              API Key\n            </label>\n            <div className=\"relative\">\n              <input\n                type={showApiKey ? 'text' : 'password'}\n                value={config.apiKey}\n                onChange={(e) => setConfig(prev => ({ ...prev, apiKey: e.target.value }))}\n                placeholder={\n                  config.provider === 'openai' ? 'sk-...' :\n                  config.provider === 'anthropic' ? 'sk-ant-...' :\n                  config.provider === 'openrouter' ? 'sk-or-...' :\n                  config.provider === 'gemini' ? 'AIza...' :\n                  'Enter your API key'\n                }\n                className=\"w-full border border-gray-300 rounded-lg px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n              <button\n                type=\"button\"\n                onClick={() => setShowApiKey(!showApiKey)}\n                className=\"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600\"\n              >\n                {showApiKey ? '🙈' : '👁️'}\n              </button>\n            </div>\n            {config.provider === 'openrouter' && (\n              <p className=\"text-xs text-gray-500 mt-1\">\n                Get your API key from <a href=\"https://openrouter.ai/keys\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-500 hover:underline\">OpenRouter Keys</a>\n              </p>\n            )}\n            {config.provider === 'gemini' && (\n              <p className=\"text-xs text-gray-500 mt-1\">\n                Get your API key from <a href=\"https://makersuite.google.com/app/apikey\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-500 hover:underline\">Google AI Studio</a>\n              </p>\n            )}\n          </div>\n\n          {config.provider === 'openrouter' && (\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Base URL\n              </label>\n              <input\n                type=\"text\"\n                value={config.baseURL}\n                onChange={(e) => setConfig(prev => ({ ...prev, baseURL: e.target.value }))}\n                placeholder=\"https://openrouter.ai/api/v1\"\n                className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n              <p className=\"text-xs text-gray-500 mt-1\">\n                OpenRouter API endpoint (default: https://openrouter.ai/api/v1)\n              </p>\n            </div>\n          )}\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Temperature: {config.temperature}\n              </label>\n              <input\n                type=\"range\"\n                min=\"0\"\n                max=\"1\"\n                step=\"0.1\"\n                value={config.temperature}\n                onChange={(e) => setConfig(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}\n                className=\"w-full\"\n              />\n              <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n                <span>Focused</span>\n                <span>Creative</span>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Max Tokens\n              </label>\n              <input\n                type=\"number\"\n                value={config.maxTokens}\n                onChange={(e) => setConfig(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}\n                min=\"100\"\n                max=\"4000\"\n                className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n          </div>\n\n          <button\n            onClick={handleTestConnection}\n            disabled={appState.isProcessing}\n            className=\"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50\"\n          >\n            <RefreshCw className={appState.isProcessing ? 'animate-spin' : ''} size={16} />\n            Test Connection\n          </button>\n        </div>\n      </div>\n\n      {/* Matching Settings */}\n      <div className=\"bg-white rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center gap-2 mb-4\">\n          <Sliders className=\"text-green-500\" size={20} />\n          <h2 className=\"text-xl font-semibold text-gray-800\">Matching Settings</h2>\n        </div>\n\n        <div className=\"space-y-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Fuzzy Match Threshold: {matchingSettings.fuzzyThreshold}\n            </label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.1\"\n              value={matchingSettings.fuzzyThreshold}\n              onChange={(e) => setMatchingSettings(prev => ({\n                ...prev,\n                fuzzyThreshold: parseFloat(e.target.value)\n              }))}\n              className=\"w-full\"\n            />\n            <p className=\"text-xs text-gray-500 mt-1\">\n              Lower values include more approximate matches\n            </p>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Semantic Threshold: {matchingSettings.semanticThreshold}\n            </label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.1\"\n              value={matchingSettings.semanticThreshold}\n              onChange={(e) => setMatchingSettings(prev => ({\n                ...prev,\n                semanticThreshold: parseFloat(e.target.value)\n              }))}\n              className=\"w-full\"\n            />\n            <p className=\"text-xs text-gray-500 mt-1\">\n              Minimum confidence for semantic similarity matches\n            </p>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Maximum Results\n            </label>\n            <input\n              type=\"number\"\n              value={matchingSettings.maxResults}\n              onChange={(e) => setMatchingSettings(prev => ({\n                ...prev,\n                maxResults: parseInt(e.target.value)\n              }))}\n              min=\"10\"\n              max=\"1000\"\n              className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* UI Settings */}\n      <div className=\"bg-white rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center gap-2 mb-4\">\n          <Palette className=\"text-purple-500\" size={20} />\n          <h2 className=\"text-xl font-semibold text-gray-800\">Interface Settings</h2>\n        </div>\n\n        <div className=\"space-y-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Theme\n            </label>\n            <select\n              value={uiSettings.theme}\n              onChange={(e) => setUISettings(prev => ({ ...prev, theme: e.target.value as any }))}\n              className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"light\">Light</option>\n              <option value=\"dark\">Dark</option>\n              <option value=\"auto\">Auto (System)</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Language\n            </label>\n            <select\n              value={uiSettings.language}\n              onChange={(e) => setUISettings(prev => ({ ...prev, language: e.target.value }))}\n              className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"en\">English</option>\n              <option value=\"fr\">Français</option>\n              <option value=\"es\">Español</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Save Button */}\n      <div className=\"flex justify-end\">\n        <button\n          onClick={handleSaveSettings}\n          className=\"flex items-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium\"\n        >\n          <Save size={16} />\n          Save All Settings\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAYe,SAAS,cAAc,EAAE,QAAQ,EAAE,cAAc,EAAsB;;IACpF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAC7C,UAAU;QACV,QAAQ;QACR,OAAO;QACP,aAAa;QACb,WAAW;QACX,SAAS;IACX;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,gBAAgB;QAChB,mBAAmB;QACnB,YAAY;IACd;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO;QACP,UAAU;IACZ;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,qBAAqB;QACzB,4DAA4D;QAC5D,QAAQ,GAAG,CAAC,oBAAoB;YAAE;YAAQ;YAAkB;QAAW;QAEvE,uBAAuB;QACvB,eAAe;YACb,OAAO;QACT;QAEA,2CAA2C;QAC3C,MAAM;IACR;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,OAAO,MAAM,EAAE;YAClB,MAAM;YACN;QACF;QAEA,oBAAoB;QACpB,IAAI;YACF,eAAe;gBAAE,cAAc;YAAK;YACpC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,MAAM;QACR,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,eAAe;gBAAE,cAAc;YAAM;QACvC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;gCAAgB,MAAM;;;;;;0CACrC,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAGtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,OAAO,OAAO,QAAQ;wCACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAClC,GAAG,IAAI;oDACP,UAAU,EAAE,MAAM,CAAC,KAAK;oDACxB,oCAAoC;oDACpC,OAAO,EAAE,MAAM,CAAC,KAAK,KAAK,WAAW,UAC9B,EAAE,MAAM,CAAC,KAAK,KAAK,cAAc,oBACjC,EAAE,MAAM,CAAC,KAAK,KAAK,eAAe,iBAClC,EAAE,MAAM,CAAC,KAAK,KAAK,WAAW,eAAe;oDACpD,qCAAqC;oDACrC,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK,eAAe,iCAAiC;gDAC9E,CAAC;wCACD,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,6LAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;;;;;;;;;;;;;0CAI1B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;oCAG/D,OAAO,QAAQ,KAAK,6BACnB,6LAAC;wCACC,MAAK;wCACL,OAAO,OAAO,KAAK;wCACnB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACtE,aAAY;wCACZ,WAAU;;;;;6DAGZ,6LAAC;wCACC,OAAO,OAAO,KAAK;wCACnB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACtE,WAAU;;4CAET,OAAO,QAAQ,KAAK,0BACnB;;kEACE,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,6LAAC;wDAAO,OAAM;kEAAc;;;;;;kEAC5B,6LAAC;wDAAO,OAAM;kEAAgB;;;;;;;;4CAGjC,OAAO,QAAQ,KAAK,6BACnB;;kEACE,6LAAC;wDAAO,OAAM;kEAAgB;;;;;;kEAC9B,6LAAC;wDAAO,OAAM;kEAAkB;;;;;;kEAChC,6LAAC;wDAAO,OAAM;kEAAiB;;;;;;;;4CAGlC,OAAO,QAAQ,KAAK,0BACnB;;kEACE,6LAAC;wDAAO,OAAM;kEAAa;;;;;;kEAC3B,6LAAC;wDAAO,OAAM;kEAAoB;;;;;;kEAClC,6LAAC;wDAAO,OAAM;kEAAiB;;;;;;kEAC/B,6LAAC;wDAAO,OAAM;kEAAmB;;;;;;;;4CAGpC,OAAO,QAAQ,KAAK,yBACnB,6LAAC;gDAAO,OAAM;0DAAU;;;;;;;;;;;;oCAI7B,OAAO,QAAQ,KAAK,8BACnB,6LAAC;wCAAE,WAAU;;4CAA6B;0DAChB,6LAAC;gDAAE,MAAK;gDAA+B,QAAO;gDAAS,KAAI;gDAAsB,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAKzJ,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAM,aAAa,SAAS;gDAC5B,OAAO,OAAO,MAAM;gDACpB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACvE,aACE,OAAO,QAAQ,KAAK,WAAW,WAC/B,OAAO,QAAQ,KAAK,cAAc,eAClC,OAAO,QAAQ,KAAK,eAAe,cACnC,OAAO,QAAQ,KAAK,WAAW,YAC/B;gDAEF,WAAU;;;;;;0DAEZ,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,cAAc,CAAC;gDAC9B,WAAU;0DAET,aAAa,OAAO;;;;;;;;;;;;oCAGxB,OAAO,QAAQ,KAAK,8BACnB,6LAAC;wCAAE,WAAU;;4CAA6B;0DAClB,6LAAC;gDAAE,MAAK;gDAA6B,QAAO;gDAAS,KAAI;gDAAsB,WAAU;0DAAgC;;;;;;;;;;;;oCAGlJ,OAAO,QAAQ,KAAK,0BACnB,6LAAC;wCAAE,WAAU;;4CAA6B;0DAClB,6LAAC;gDAAE,MAAK;gDAA2C,QAAO;gDAAS,KAAI;gDAAsB,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;4BAKlK,OAAO,QAAQ,KAAK,8BACnB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO,OAAO,OAAO;wCACrB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACxE,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAM9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;;oDAA+C;oDAChD,OAAO,WAAW;;;;;;;0DAElC,6LAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,MAAK;gDACL,OAAO,OAAO,WAAW;gDACzB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDACxF,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;kDAIV,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,OAAO,SAAS;gDACvB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,WAAW,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDACpF,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;;;;;;;;0CAKhB,6LAAC;gCACC,SAAS;gCACT,UAAU,SAAS,YAAY;gCAC/B,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAW,SAAS,YAAY,GAAG,iBAAiB;wCAAI,MAAM;;;;;;oCAAM;;;;;;;;;;;;;;;;;;;0BAOrF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,UAAO;gCAAC,WAAU;gCAAiB,MAAM;;;;;;0CAC1C,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAGtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAA+C;4CACtC,iBAAiB,cAAc;;;;;;;kDAEzD,6LAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,MAAK;wCACL,OAAO,iBAAiB,cAAc;wCACtC,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;oDAC5C,GAAG,IAAI;oDACP,gBAAgB,WAAW,EAAE,MAAM,CAAC,KAAK;gDAC3C,CAAC;wCACD,WAAU;;;;;;kDAEZ,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAK5C,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAA+C;4CACzC,iBAAiB,iBAAiB;;;;;;;kDAEzD,6LAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,MAAK;wCACL,OAAO,iBAAiB,iBAAiB;wCACzC,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;oDAC5C,GAAG,IAAI;oDACP,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK;gDAC9C,CAAC;wCACD,WAAU;;;;;;kDAEZ,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAK5C,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO,iBAAiB,UAAU;wCAClC,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;oDAC5C,GAAG,IAAI;oDACP,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK;gDACrC,CAAC;wCACD,KAAI;wCACJ,KAAI;wCACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAOlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;gCAAkB,MAAM;;;;;;0CAC3C,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAGtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,OAAO,WAAW,KAAK;wCACvB,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAQ,CAAC;wCACjF,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;;;;;;;;;;;;;0CAIzB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,OAAO,WAAW,QAAQ;wCAC1B,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC7E,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,6LAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,6LAAC;gDAAO,OAAM;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,6LAAC,qMAAA,CAAA,OAAI;4BAAC,MAAM;;;;;;wBAAM;;;;;;;;;;;;;;;;;;AAM5B;GA9WwB;KAAA"}}, {"offset": {"line": 2802, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2808, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/csv-query-app/src/components/AnalyticsView.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { BarChart3, TrendingUp, Users, Search, Clock, Target } from 'lucide-react';\nimport { AppState } from '@/types';\n\ninterface AnalyticsViewProps {\n  appState: AppState;\n  updateAppState: (updates: Partial<AppState>) => void;\n}\n\nexport default function AnalyticsView({ appState, updateAppState }: AnalyticsViewProps) {\n  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  // Mock analytics data\n  const analyticsData = {\n    totalContacts: appState.allContacts.length,\n    totalQueries: 47,\n    averageMatchScore: 0.78,\n    topIndustries: [\n      { name: 'Grocery Retail', count: 156 },\n      { name: 'Food Service', count: 89 },\n      { name: 'Manufacturing', count: 67 },\n      { name: 'Technology', count: 45 },\n      { name: 'Healthcare', count: 34 },\n    ],\n    topLocations: [\n      { name: 'Toronto, ON', count: 89 },\n      { name: 'Vancouver, BC', count: 67 },\n      { name: 'Montreal, QC', count: 56 },\n      { name: 'Calgary, AB', count: 45 },\n      { name: 'Ottawa, ON', count: 34 },\n    ],\n    queryHistory: isClient ? [\n      { query: 'category managers grocery chains', timestamp: new Date('2024-01-15'), resultCount: 23 },\n      { query: 'bakeries Alberta', timestamp: new Date('2024-01-14'), resultCount: 15 },\n      { query: 'food service directors', timestamp: new Date('2024-01-13'), resultCount: 31 },\n      { query: 'retail managers Toronto', timestamp: new Date('2024-01-12'), resultCount: 18 },\n      { query: 'procurement specialists', timestamp: new Date('2024-01-11'), resultCount: 27 },\n    ] : [],\n    performanceMetrics: {\n      averageQueryTime: 1.2,\n      successRate: 0.94,\n      userSatisfaction: 0.87,\n    },\n    usageStats: {\n      dailyQueries: [12, 15, 8, 23, 19, 16, 21],\n      weeklyUploads: [3, 1, 2, 4, 2, 1, 3],\n    }\n  };\n\n  const StatCard = ({ icon: Icon, title, value, subtitle, color }: {\n    icon: any;\n    title: string;\n    value: string | number;\n    subtitle?: string;\n    color: string;\n  }) => (\n    <div className=\"bg-white rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className=\"text-sm font-medium text-gray-600\">{title}</p>\n          <p className=\"text-2xl font-bold text-gray-900\">{value}</p>\n          {subtitle && <p className=\"text-sm text-gray-500\">{subtitle}</p>}\n        </div>\n        <div className={`p-3 rounded-full ${color}`}>\n          <Icon size={24} className=\"text-white\" />\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-xl font-semibold text-gray-800\">Analytics Dashboard</h2>\n            <p className=\"text-sm text-gray-600\">\n              Insights into your contact filtering performance\n            </p>\n          </div>\n\n          <select\n            value={timeRange}\n            onChange={(e) => setTimeRange(e.target.value as any)}\n            className=\"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"7d\">Last 7 days</option>\n            <option value=\"30d\">Last 30 days</option>\n            <option value=\"90d\">Last 90 days</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <StatCard\n          icon={Users}\n          title=\"Total Contacts\"\n          value={analyticsData.totalContacts.toLocaleString()}\n          subtitle=\"Across all files\"\n          color=\"bg-blue-500\"\n        />\n        <StatCard\n          icon={Search}\n          title=\"Total Queries\"\n          value={analyticsData.totalQueries}\n          subtitle={`${timeRange} period`}\n          color=\"bg-green-500\"\n        />\n        <StatCard\n          icon={Target}\n          title=\"Avg Match Score\"\n          value={`${(analyticsData.averageMatchScore * 100).toFixed(1)}%`}\n          subtitle=\"Query accuracy\"\n          color=\"bg-purple-500\"\n        />\n        <StatCard\n          icon={Clock}\n          title=\"Avg Query Time\"\n          value={`${analyticsData.performanceMetrics.averageQueryTime}s`}\n          subtitle=\"Response speed\"\n          color=\"bg-orange-500\"\n        />\n      </div>\n\n      {/* Performance Metrics */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Success Rate</h3>\n          <div className=\"flex items-center justify-center\">\n            <div className=\"relative w-32 h-32\">\n              <svg className=\"w-32 h-32 transform -rotate-90\" viewBox=\"0 0 36 36\">\n                <path\n                  d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                  fill=\"none\"\n                  stroke=\"#e5e7eb\"\n                  strokeWidth=\"3\"\n                />\n                <path\n                  d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                  fill=\"none\"\n                  stroke=\"#10b981\"\n                  strokeWidth=\"3\"\n                  strokeDasharray={`${analyticsData.performanceMetrics.successRate * 100}, 100`}\n                />\n              </svg>\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <span className=\"text-2xl font-bold text-gray-900\">\n                  {(analyticsData.performanceMetrics.successRate * 100).toFixed(0)}%\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">User Satisfaction</h3>\n          <div className=\"flex items-center justify-center\">\n            <div className=\"relative w-32 h-32\">\n              <svg className=\"w-32 h-32 transform -rotate-90\" viewBox=\"0 0 36 36\">\n                <path\n                  d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                  fill=\"none\"\n                  stroke=\"#e5e7eb\"\n                  strokeWidth=\"3\"\n                />\n                <path\n                  d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                  fill=\"none\"\n                  stroke=\"#3b82f6\"\n                  strokeWidth=\"3\"\n                  strokeDasharray={`${analyticsData.performanceMetrics.userSatisfaction * 100}, 100`}\n                />\n              </svg>\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <span className=\"text-2xl font-bold text-gray-900\">\n                  {(analyticsData.performanceMetrics.userSatisfaction * 100).toFixed(0)}%\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Daily Queries</h3>\n          <div className=\"space-y-2\">\n            {analyticsData.usageStats.dailyQueries.map((queries, index) => (\n              <div key={index} className=\"flex items-center gap-3\">\n                <span className=\"text-sm text-gray-600 w-8\">\n                  {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][index]}\n                </span>\n                <div className=\"flex-1 bg-gray-200 rounded-full h-2\">\n                  <div\n                    className=\"bg-blue-500 h-2 rounded-full\"\n                    style={{ width: `${(queries / Math.max(...analyticsData.usageStats.dailyQueries)) * 100}%` }}\n                  />\n                </div>\n                <span className=\"text-sm font-medium text-gray-900 w-6\">{queries}</span>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Top Industries and Locations */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Top Industries</h3>\n          <div className=\"space-y-3\">\n            {analyticsData.topIndustries.map((industry, index) => (\n              <div key={industry.name} className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <span className=\"text-sm font-medium text-gray-600\">#{index + 1}</span>\n                  <span className=\"text-sm text-gray-900\">{industry.name}</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-20 bg-gray-200 rounded-full h-2\">\n                    <div\n                      className=\"bg-blue-500 h-2 rounded-full\"\n                      style={{\n                        width: `${(industry.count / Math.max(...analyticsData.topIndustries.map(i => i.count))) * 100}%`\n                      }}\n                    />\n                  </div>\n                  <span className=\"text-sm font-medium text-gray-900 w-8\">{industry.count}</span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Top Locations</h3>\n          <div className=\"space-y-3\">\n            {analyticsData.topLocations.map((location, index) => (\n              <div key={location.name} className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <span className=\"text-sm font-medium text-gray-600\">#{index + 1}</span>\n                  <span className=\"text-sm text-gray-900\">{location.name}</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-20 bg-gray-200 rounded-full h-2\">\n                    <div\n                      className=\"bg-green-500 h-2 rounded-full\"\n                      style={{\n                        width: `${(location.count / Math.max(...analyticsData.topLocations.map(l => l.count))) * 100}%`\n                      }}\n                    />\n                  </div>\n                  <span className=\"text-sm font-medium text-gray-900 w-8\">{location.count}</span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Query History */}\n      <div className=\"bg-white rounded-lg shadow-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Recent Query History</h3>\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full text-sm\">\n            <thead>\n              <tr className=\"border-b\">\n                <th className=\"text-left py-2 text-gray-600\">Query</th>\n                <th className=\"text-left py-2 text-gray-600\">Date</th>\n                <th className=\"text-left py-2 text-gray-600\">Results</th>\n                <th className=\"text-left py-2 text-gray-600\">Performance</th>\n              </tr>\n            </thead>\n            <tbody>\n              {analyticsData.queryHistory.map((query, index) => (\n                <tr key={index} className=\"border-b hover:bg-gray-50\">\n                  <td className=\"py-3 font-medium text-gray-900\">{query.query}</td>\n                  <td className=\"py-3 text-gray-600\">\n                    {isClient ? query.timestamp.toLocaleDateString() : 'Loading...'}\n                  </td>\n                  <td className=\"py-3 text-gray-600\">{query.resultCount} contacts</td>\n                  <td className=\"py-3\">\n                    <span className=\"px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs\">\n                      Excellent\n                    </span>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAWe,SAAS,cAAc,EAAE,QAAQ,EAAE,cAAc,EAAsB;;IACpF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,YAAY;QACd;kCAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,gBAAgB;QACpB,eAAe,SAAS,WAAW,CAAC,MAAM;QAC1C,cAAc;QACd,mBAAmB;QACnB,eAAe;YACb;gBAAE,MAAM;gBAAkB,OAAO;YAAI;YACrC;gBAAE,MAAM;gBAAgB,OAAO;YAAG;YAClC;gBAAE,MAAM;gBAAiB,OAAO;YAAG;YACnC;gBAAE,MAAM;gBAAc,OAAO;YAAG;YAChC;gBAAE,MAAM;gBAAc,OAAO;YAAG;SACjC;QACD,cAAc;YACZ;gBAAE,MAAM;gBAAe,OAAO;YAAG;YACjC;gBAAE,MAAM;gBAAiB,OAAO;YAAG;YACnC;gBAAE,MAAM;gBAAgB,OAAO;YAAG;YAClC;gBAAE,MAAM;gBAAe,OAAO;YAAG;YACjC;gBAAE,MAAM;gBAAc,OAAO;YAAG;SACjC;QACD,cAAc,WAAW;YACvB;gBAAE,OAAO;gBAAoC,WAAW,IAAI,KAAK;gBAAe,aAAa;YAAG;YAChG;gBAAE,OAAO;gBAAoB,WAAW,IAAI,KAAK;gBAAe,aAAa;YAAG;YAChF;gBAAE,OAAO;gBAA0B,WAAW,IAAI,KAAK;gBAAe,aAAa;YAAG;YACtF;gBAAE,OAAO;gBAA2B,WAAW,IAAI,KAAK;gBAAe,aAAa;YAAG;YACvF;gBAAE,OAAO;gBAA2B,WAAW,IAAI,KAAK;gBAAe,aAAa;YAAG;SACxF,GAAG,EAAE;QACN,oBAAoB;YAClB,kBAAkB;YAClB,aAAa;YACb,kBAAkB;QACpB;QACA,YAAY;YACV,cAAc;gBAAC;gBAAI;gBAAI;gBAAG;gBAAI;gBAAI;gBAAI;aAAG;YACzC,eAAe;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;aAAE;QACtC;IACF;IAEA,MAAM,WAAW,CAAC,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAM5D,iBACC,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;4BAChD,0BAAY,6LAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAErD,6LAAC;wBAAI,WAAW,CAAC,iBAAiB,EAAE,OAAO;kCACzC,cAAA,6LAAC;4BAAK,MAAM;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;IAMlC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4BAC5C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAK;;;;;;8CACnB,6LAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,6LAAC;oCAAO,OAAM;8CAAM;;;;;;;;;;;;;;;;;;;;;;;0BAM1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAM,uMAAA,CAAA,QAAK;wBACX,OAAM;wBACN,OAAO,cAAc,aAAa,CAAC,cAAc;wBACjD,UAAS;wBACT,OAAM;;;;;;kCAER,6LAAC;wBACC,MAAM,yMAAA,CAAA,SAAM;wBACZ,OAAM;wBACN,OAAO,cAAc,YAAY;wBACjC,UAAU,GAAG,UAAU,OAAO,CAAC;wBAC/B,OAAM;;;;;;kCAER,6LAAC;wBACC,MAAM,yMAAA,CAAA,SAAM;wBACZ,OAAM;wBACN,OAAO,GAAG,CAAC,cAAc,iBAAiB,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;wBAC/D,UAAS;wBACT,OAAM;;;;;;kCAER,6LAAC;wBACC,MAAM,uMAAA,CAAA,QAAK;wBACX,OAAM;wBACN,OAAO,GAAG,cAAc,kBAAkB,CAAC,gBAAgB,CAAC,CAAC,CAAC;wBAC9D,UAAS;wBACT,OAAM;;;;;;;;;;;;0BAKV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;4CAAiC,SAAQ;;8DACtD,6LAAC;oDACC,GAAE;oDACF,MAAK;oDACL,QAAO;oDACP,aAAY;;;;;;8DAEd,6LAAC;oDACC,GAAE;oDACF,MAAK;oDACL,QAAO;oDACP,aAAY;oDACZ,iBAAiB,GAAG,cAAc,kBAAkB,CAAC,WAAW,GAAG,IAAI,KAAK,CAAC;;;;;;;;;;;;sDAGjF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;;oDACb,CAAC,cAAc,kBAAkB,CAAC,WAAW,GAAG,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;4CAAiC,SAAQ;;8DACtD,6LAAC;oDACC,GAAE;oDACF,MAAK;oDACL,QAAO;oDACP,aAAY;;;;;;8DAEd,6LAAC;oDACC,GAAE;oDACF,MAAK;oDACL,QAAO;oDACP,aAAY;oDACZ,iBAAiB,GAAG,cAAc,kBAAkB,CAAC,gBAAgB,GAAG,IAAI,KAAK,CAAC;;;;;;;;;;;;sDAGtF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;;oDACb,CAAC,cAAc,kBAAkB,CAAC,gBAAgB,GAAG,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAI,WAAU;0CACZ,cAAc,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,SAAS,sBACnD,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAK,WAAU;0DACb;oDAAC;oDAAO;oDAAO;oDAAO;oDAAO;oDAAO;oDAAO;iDAAM,CAAC,MAAM;;;;;;0DAE3D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,AAAC,UAAU,KAAK,GAAG,IAAI,cAAc,UAAU,CAAC,YAAY,IAAK,IAAI,CAAC,CAAC;oDAAC;;;;;;;;;;;0DAG/F,6LAAC;gDAAK,WAAU;0DAAyC;;;;;;;uCAVjD;;;;;;;;;;;;;;;;;;;;;;0BAkBlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAI,WAAU;0CACZ,cAAc,aAAa,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC1C,6LAAC;wCAAwB,WAAU;;0DACjC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;4DAAoC;4DAAE,QAAQ;;;;;;;kEAC9D,6LAAC;wDAAK,WAAU;kEAAyB,SAAS,IAAI;;;;;;;;;;;;0DAExD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAU;4DACV,OAAO;gEACL,OAAO,GAAG,AAAC,SAAS,KAAK,GAAG,KAAK,GAAG,IAAI,cAAc,aAAa,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,KAAM,IAAI,CAAC,CAAC;4DAClG;;;;;;;;;;;kEAGJ,6LAAC;wDAAK,WAAU;kEAAyC,SAAS,KAAK;;;;;;;;;;;;;uCAdjE,SAAS,IAAI;;;;;;;;;;;;;;;;kCAqB7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAI,WAAU;0CACZ,cAAc,YAAY,CAAC,GAAG,CAAC,CAAC,UAAU,sBACzC,6LAAC;wCAAwB,WAAU;;0DACjC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;4DAAoC;4DAAE,QAAQ;;;;;;;kEAC9D,6LAAC;wDAAK,WAAU;kEAAyB,SAAS,IAAI;;;;;;;;;;;;0DAExD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAU;4DACV,OAAO;gEACL,OAAO,GAAG,AAAC,SAAS,KAAK,GAAG,KAAK,GAAG,IAAI,cAAc,YAAY,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,KAAM,IAAI,CAAC,CAAC;4DACjG;;;;;;;;;;;kEAGJ,6LAAC;wDAAK,WAAU;kEAAyC,SAAS,KAAK;;;;;;;;;;;;;uCAdjE,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;0BAuB/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;8CACC,cAAA,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,6LAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,6LAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,6LAAC;gDAAG,WAAU;0DAA+B;;;;;;;;;;;;;;;;;8CAGjD,6LAAC;8CACE,cAAc,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,sBACtC,6LAAC;4CAAe,WAAU;;8DACxB,6LAAC;oDAAG,WAAU;8DAAkC,MAAM,KAAK;;;;;;8DAC3D,6LAAC;oDAAG,WAAU;8DACX,WAAW,MAAM,SAAS,CAAC,kBAAkB,KAAK;;;;;;8DAErD,6LAAC;oDAAG,WAAU;;wDAAsB,MAAM,WAAW;wDAAC;;;;;;;8DACtD,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAU;kEAA6D;;;;;;;;;;;;2CAPxE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBzB;GAhSwB;KAAA"}}, {"offset": {"line": 3724, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3730, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/csv-query-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/Tabs';\nimport { MessageSquare, Database, Search, Settings, BarChart3 } from 'lucide-react';\nimport ChatInterface from '@/components/ChatInterface';\nimport DataManagement from '@/components/DataManagement';\nimport ResultsView from '@/components/ResultsView';\nimport SettingsPanel from '@/components/SettingsPanel';\nimport AnalyticsView from '@/components/AnalyticsView';\nimport { AppState } from '@/types';\n\nexport default function Home() {\n  const [appState, setAppState] = useState<AppState>({\n    activeTab: 'chat',\n    uploadedFiles: [],\n    allContacts: [],\n    currentResults: null,\n    isProcessing: false,\n    error: null,\n  });\n\n  const updateAppState = (updates: Partial<AppState>) => {\n    setAppState(prev => ({ ...prev, ...updates }));\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">\n            AI Contact Filtering System\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Intelligent contact management with natural language filtering\n          </p>\n        </div>\n\n        {/* Main Application */}\n        <Tabs\n          value={appState.activeTab}\n          onValueChange={(value) => updateAppState({ activeTab: value as AppState['activeTab'] })}\n          className=\"w-full\"\n        >\n          <TabsList className=\"grid w-full grid-cols-5 bg-white rounded-lg shadow-sm border gap-1 p-1\">\n            <TabsTrigger\n              value=\"chat\"\n              className=\"flex items-center gap-2 px-4 py-3\"\n            >\n              <MessageSquare size={18} />\n              Chat\n            </TabsTrigger>\n            <TabsTrigger\n              value=\"data\"\n              className=\"flex items-center gap-2 px-4 py-3\"\n            >\n              <Database size={18} />\n              Data\n            </TabsTrigger>\n            <TabsTrigger\n              value=\"results\"\n              className=\"flex items-center gap-2 px-4 py-3\"\n            >\n              <Search size={18} />\n              Results\n            </TabsTrigger>\n            <TabsTrigger\n              value=\"analytics\"\n              className=\"flex items-center gap-2 px-4 py-3\"\n            >\n              <BarChart3 size={18} />\n              Analytics\n            </TabsTrigger>\n            <TabsTrigger\n              value=\"settings\"\n              className=\"flex items-center gap-2 px-4 py-3\"\n            >\n              <Settings size={18} />\n              Settings\n            </TabsTrigger>\n          </TabsList>\n\n          <div className=\"mt-6\">\n            <TabsContent value=\"chat\" className=\"space-y-4\">\n              <ChatInterface\n                appState={appState}\n                updateAppState={updateAppState}\n              />\n            </TabsContent>\n\n            <TabsContent value=\"data\" className=\"space-y-4\">\n              <DataManagement\n                appState={appState}\n                updateAppState={updateAppState}\n              />\n            </TabsContent>\n\n            <TabsContent value=\"results\" className=\"space-y-4\">\n              <ResultsView\n                appState={appState}\n                updateAppState={updateAppState}\n              />\n            </TabsContent>\n\n            <TabsContent value=\"analytics\" className=\"space-y-4\">\n              <AnalyticsView\n                appState={appState}\n                updateAppState={updateAppState}\n              />\n            </TabsContent>\n\n            <TabsContent value=\"settings\" className=\"space-y-4\">\n              <SettingsPanel\n                appState={appState}\n                updateAppState={updateAppState}\n              />\n            </TabsContent>\n          </div>\n        </Tabs>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AALA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,WAAW;QACX,eAAe,EAAE;QACjB,aAAa,EAAE;QACf,gBAAgB;QAChB,cAAc;QACd,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,CAAC;IAC9C;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAMvC,6LAAC,mIAAA,CAAA,OAAI;oBACH,OAAO,SAAS,SAAS;oBACzB,eAAe,CAAC,QAAU,eAAe;4BAAE,WAAW;wBAA+B;oBACrF,WAAU;;sCAEV,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,mIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;;sDAEV,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,MAAM;;;;;;wCAAM;;;;;;;8CAG7B,6LAAC,mIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;wCAAM;;;;;;;8CAGxB,6LAAC,mIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;;sDAEV,6LAAC,yMAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;wCAAM;;;;;;;8CAGtB,6LAAC,mIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;;sDAEV,6LAAC,qNAAA,CAAA,YAAS;4CAAC,MAAM;;;;;;wCAAM;;;;;;;8CAGzB,6LAAC,mIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;wCAAM;;;;;;;;;;;;;sCAK1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAO,WAAU;8CAClC,cAAA,6LAAC,sIAAA,CAAA,UAAa;wCACZ,UAAU;wCACV,gBAAgB;;;;;;;;;;;8CAIpB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAO,WAAU;8CAClC,cAAA,6LAAC,uIAAA,CAAA,UAAc;wCACb,UAAU;wCACV,gBAAgB;;;;;;;;;;;8CAIpB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACrC,cAAA,6LAAC,oIAAA,CAAA,UAAW;wCACV,UAAU;wCACV,gBAAgB;;;;;;;;;;;8CAIpB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAY,WAAU;8CACvC,cAAA,6LAAC,sIAAA,CAAA,UAAa;wCACZ,UAAU;wCACV,gBAAgB;;;;;;;;;;;8CAIpB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CACtC,cAAA,6LAAC,sIAAA,CAAA,UAAa;wCACZ,UAAU;wCACV,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;GA/GwB;KAAA"}}, {"offset": {"line": 4023, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}