'use client';

import { useState, useCallback, useEffect } from 'react';
import { Upload, FileText, Trash2, Eye, Download } from 'lucide-react';
import { AppState, CSVFile, Contact } from '@/types';
import Papa from 'papaparse';

interface DataManagementProps {
  appState: AppState;
  updateAppState: (updates: Partial<AppState>) => void;
}

export default function DataManagement({ appState, updateAppState }: DataManagementProps) {
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  }, []);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files));
    }
  };

  const handleFiles = async (files: File[]) => {
    setUploading(true);

    for (const file of files) {
      if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
        await processCSVFile(file);
      }
    }

    setUploading(false);
  };

  const processCSVFile = (file: File): Promise<void> => {
    return new Promise((resolve) => {
      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          const headers = results.meta.fields || [];
          const data = results.data as any[];

          // Convert to Contact objects with unique IDs
          const contacts: Contact[] = data.map((row, index) => ({
            id: `${file.name}-${index}`,
            ...row,
          }));

          const csvFile: CSVFile = {
            id: `${file.name}-${Date.now()}`,
            name: file.name,
            size: file.size,
            uploadDate: new Date(),
            headers,
            rowCount: contacts.length,
            data: contacts,
          };

          // Update app state
          updateAppState({
            uploadedFiles: [...appState.uploadedFiles, csvFile],
            allContacts: [...appState.allContacts, ...contacts],
          });

          resolve();
        },
        error: (error) => {
          console.error('Error parsing CSV:', error);
          updateAppState({
            error: `Error parsing ${file.name}: ${error.message}`,
          });
          resolve();
        },
      });
    });
  };

  const removeFile = (fileId: string) => {
    const fileToRemove = appState.uploadedFiles.find(f => f.id === fileId);
    if (!fileToRemove) return;

    // Remove contacts from this file
    const remainingContacts = appState.allContacts.filter(
      contact => !contact.id.startsWith(fileToRemove.name)
    );

    updateAppState({
      uploadedFiles: appState.uploadedFiles.filter(f => f.id !== fileId),
      allContacts: remainingContacts,
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Upload Contact Data</h2>

        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragActive
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-lg font-medium text-gray-900 mb-2">
            Drop CSV files here, or click to select
          </p>
          <p className="text-sm text-gray-500 mb-4">
            Supports CSV files with contact information
          </p>

          <input
            type="file"
            multiple
            accept=".csv"
            onChange={handleFileInput}
            className="hidden"
            id="file-upload"
          />
          <label
            htmlFor="file-upload"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 cursor-pointer"
          >
            Select Files
          </label>
        </div>

        {uploading && (
          <div className="mt-4 text-center">
            <div className="inline-flex items-center px-4 py-2 text-sm text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              Processing files...
            </div>
          </div>
        )}
      </div>

      {/* Uploaded Files */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">
          Uploaded Files ({appState.uploadedFiles.length})
        </h2>

        {appState.uploadedFiles.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <FileText className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <p>No files uploaded yet</p>
            <p className="text-sm">Upload CSV files to get started</p>
          </div>
        ) : (
          <div className="space-y-3">
            {appState.uploadedFiles.map((file) => (
              <div
                key={file.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex items-center space-x-3">
                  <FileText className="h-8 w-8 text-blue-500" />
                  <div>
                    <h3 className="font-medium text-gray-900">{file.name}</h3>
                    <p className="text-sm text-gray-500">
                      {file.rowCount} contacts • {formatFileSize(file.size)}
                      {isClient && (
                        <> • Uploaded {file.uploadDate.toLocaleDateString()}</>
                      )}
                    </p>
                    <p className="text-xs text-gray-400">
                      Fields: {file.headers.join(', ')}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    className="p-2 text-gray-400 hover:text-blue-500 rounded-lg hover:bg-blue-50"
                    title="Preview data"
                  >
                    <Eye size={16} />
                  </button>
                  <button
                    className="p-2 text-gray-400 hover:text-green-500 rounded-lg hover:bg-green-50"
                    title="Download"
                  >
                    <Download size={16} />
                  </button>
                  <button
                    onClick={() => removeFile(file.id)}
                    className="p-2 text-gray-400 hover:text-red-500 rounded-lg hover:bg-red-50"
                    title="Remove file"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Data Summary */}
      {appState.allContacts.length > 0 && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Data Summary</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-900">Total Contacts</h3>
              <p className="text-2xl font-bold text-blue-600">
                {appState.allContacts.length.toLocaleString()}
              </p>
            </div>

            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-medium text-green-900">Files Loaded</h3>
              <p className="text-2xl font-bold text-green-600">
                {appState.uploadedFiles.length}
              </p>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="font-medium text-purple-900">Ready for AI</h3>
              <p className="text-2xl font-bold text-purple-600">
                ✓ Yes
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
