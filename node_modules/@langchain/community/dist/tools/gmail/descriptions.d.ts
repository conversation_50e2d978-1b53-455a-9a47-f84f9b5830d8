export declare const CREATE_DRAFT_DESCRIPTION = "A tool for creating draft emails in Gmail.\n\nINPUT example:\n{\n  \"message\": \"Hello, this is a test draft\",\n  \"to\": [\"<EMAIL>\", \"<EMAIL>\"],\n  \"subject\": \"Test Draft\",\n  \"cc\": [\"<EMAIL>\"],\n  \"bcc\": [\"<EMAIL>\"]\n}\n\nOUTPUT:\nThe output is a confirmation message with the draft ID.\n";
export declare const GET_MESSAGE_DESCRIPTION = "A tool for retrieving a specific email message from Gmail using its message ID.\n\nINPUT example:\n{\n  \"messageId\": \"unique_message_id_string\"\n}\n\nOUTPUT:\nThe output includes detailed information about the retrieved email message. This includes the subject, body, sender (from), recipients (to), date of the email, and the message ID. If any of these details are not available in the email, the tool will throw an error indicating the missing information.\n\nExample Output:\n\"Result for the prompt unique_message_id_string\n{\n  'subject': 'Email Subject',\n  'body': 'Email Body Content',\n  'from': '<EMAIL>',\n  'to': '<EMAIL>',\n  'date': 'Email Date',\n  'messageId': 'unique_message_id_string'\n}\"\n";
export declare const GET_THREAD_DESCRIPTION = "A tool for retrieving an entire email thread from Gmail using the thread ID.\n\nINPUT example:\n{\n  \"threadId\": \"unique_thread_id_string\"\n}\n\nOUTPUT:\nThe output includes an array of all the messages in the specified thread. Each message in the array contains detailed information including the subject, body, sender (from), recipients (to), date of the email, and the message ID. If any of these details are not available in a message, the tool will throw an error indicating the missing information.\n\nExample Output:\n\"Result for the prompt unique_thread_id_string\n[\n  {\n    'subject': 'Email Subject',\n    'body': 'Email Body Content',\n    'from': '<EMAIL>',\n    'to': '<EMAIL>',\n    'date': 'Email Date',\n    'messageId': 'unique_message_id_string'\n  },\n  ... (other messages in the thread)\n]\"\n";
export declare const SEND_MESSAGE_DESCRIPTION = "A tool for sending an email message using Gmail. It allows users to specify recipients, subject, and the content of the message, along with optional cc and bcc fields.\n\nINPUT example:\n{\n  \"message\": \"Hello, this is a test email\",\n  \"to\": [\"<EMAIL>\", \"<EMAIL>\"],\n  \"subject\": \"Test Email\",\n  \"cc\": [\"<EMAIL>\"],\n  \"bcc\": [\"<EMAIL>\"]\n}\n\nOUTPUT:\nThe output is a confirmation message with the ID of the sent email. If there is an error during the sending process, the tool will throw an error with a description of the problem.\n\nExample Output:\n\"Message sent. Message Id: unique_message_id_string\"\n";
export declare const SEARCH_DESCRIPTION = "A tool for searching Gmail messages or threads using a specific query. Offers the flexibility to choose between messages and threads as the search resource.\n\nINPUT:\n{\n  \"query\": \"search query\",\n  \"maxResults\": 10, // Optional: number of results to return\n  \"resource\": \"messages\" // Optional: can be \"messages\" or \"threads\"\n}\n\nOUTPUT:\nJSON list of matching email messages or threads based on the specified resource. If no data is returned, or if the specified resource is invalid, throw error with a relevant message.\n\nExample result for messages:\n\"[{\n  'id': 'message_id',\n  'threadId': 'thread_id',\n  'snippet': 'message snippet',\n  'body': 'message body',\n  'subject': 'message subject',\n  'sender': 'message sender'\n}, \n... (other messages matching the query)\n]\"\n\nExample result for threads:\n\"[{\n  'id': 'thread_id',\n  'snippet': 'thread snippet',\n  'body': 'first message body',\n  'subject': 'first message subject',\n  'sender': 'first message sender'\n},\n... (other threads matching the query)\n]\"";
